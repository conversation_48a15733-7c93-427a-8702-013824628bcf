import React from "react"
import {
  <PERSON>,
  Car<PERSON>ianGrid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON>ontaine<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample age distribution by employee type
const sampleAgeDistribution = [
  // Full-time employee age distribution
  { employeeType: "Full-time", age: 22, count: 3 },
  { employeeType: "Full-time", age: 25, count: 8 },
  { employeeType: "Full-time", age: 28, count: 14 },
  { employeeType: "Full-time", age: 31, count: 20 },
  { employeeType: "Full-time", age: 34, count: 25 },
  { employeeType: "Full-time", age: 37, count: 28 },
  { employeeType: "Full-time", age: 40, count: 32 },
  { employeeType: "Full-time", age: 43, count: 26 },
  { employeeType: "Full-time", age: 46, count: 22 },
  { employeeType: "Full-time", age: 49, count: 18 },
  { employeeType: "Full-time", age: 52, count: 14 },
  { employeeType: "Full-time", age: 55, count: 10 },
  { employeeType: "Full-time", age: 58, count: 6 },
  { employeeType: "Full-time", age: 61, count: 4 },
  { employeeType: "Full-time", age: 64, count: 2 },

  // Part-time employee age distribution
  { employeeType: "Part-time", age: 22, count: 2 },
  { employeeType: "Part-time", age: 25, count: 3 },
  { employeeType: "Part-time", age: 28, count: 4 },
  { employeeType: "Part-time", age: 31, count: 6 },
  { employeeType: "Part-time", age: 34, count: 9 },
  { employeeType: "Part-time", age: 37, count: 12 },
  { employeeType: "Part-time", age: 40, count: 14 },
  { employeeType: "Part-time", age: 43, count: 12 },
  { employeeType: "Part-time", age: 46, count: 9 },
  { employeeType: "Part-time", age: 49, count: 7 },
  { employeeType: "Part-time", age: 52, count: 5 },
  { employeeType: "Part-time", age: 55, count: 3 },
  { employeeType: "Part-time", age: 58, count: 2 },
  { employeeType: "Part-time", age: 61, count: 1 },
  { employeeType: "Part-time", age: 64, count: 1 },
]

// Sample staff by role and employee type
const sampleStaffByRole = [
  { role: "Physicians", fullTime: 65, partTime: 15 },
  { role: "Nurses", fullTime: 75, partTime: 25 },
  { role: "Admin", fullTime: 80, partTime: 20 },
  { role: "Lab Techs", fullTime: 85, partTime: 15 },
  { role: "Management", fullTime: 95, partTime: 5 },
  { role: "Support Staff", fullTime: 60, partTime: 40 },
]

// Create violin plot data by transforming the age distribution
const createViolinPlotData = () => {
  // Group by employee type
  const fullTimeData = sampleAgeDistribution.filter(
    (item) => item.employeeType === "Full-time"
  )
  const partTimeData = sampleAgeDistribution.filter(
    (item) => item.employeeType === "Part-time"
  )

  // Find min and max ages
  const allAges = sampleAgeDistribution.map((item) => item.age)
  const minAge = Math.min(...allAges)
  const maxAge = Math.max(...allAges)

  // Create violin plot data points
  const violinData = []

  // For each age, create mirrored points for full-time and part-time
  for (let age = minAge; age <= maxAge; age += 3) {
    const fullTimePoint = fullTimeData.find((item) => item.age === age)
    const partTimePoint = partTimeData.find((item) => item.age === age)

    const fullTimeCount = fullTimePoint ? fullTimePoint.count : 0
    const partTimeCount = partTimePoint ? partTimePoint.count : 0

    // Create mirrored points for violin plot
    violinData.push({
      age,
      employeeType: "Full-time",
      count: fullTimeCount,
      // Negative values for left side of violin
      value: -fullTimeCount,
    })

    violinData.push({
      age,
      employeeType: "Part-time",
      count: partTimeCount,
      // Positive values for right side of violin
      value: partTimeCount,
    })
  }

  return violinData
}

const violinPlotData = createViolinPlotData()

interface StaffDemographicsChartProps {
  data?: typeof violinPlotData
}

const StaffDemographicsChart: React.FC<StaffDemographicsChartProps> = ({
  data = violinPlotData,
}) => {
  // Calculate overall metrics
  const totalStaff = sampleStaffByRole.reduce(
    (sum, item) => sum + item.fullTime + item.partTime,
    0
  )
  const fullTimeCount = sampleStaffByRole.reduce(
    (sum, item) => sum + item.fullTime,
    0
  )
  const partTimeCount = sampleStaffByRole.reduce(
    (sum, item) => sum + item.partTime,
    0
  )
  const fullTimePercent = Math.round((fullTimeCount / totalStaff) * 100)
  const partTimePercent = Math.round((partTimeCount / totalStaff) * 100)

  // Calculate average age
  const totalAgeWeighted = sampleAgeDistribution.reduce(
    (sum, item) => sum + item.age * item.count,
    0
  )
  const totalCount = sampleAgeDistribution.reduce(
    (sum, item) => sum + item.count,
    0
  )
  const averageAge = Math.round(totalAgeWeighted / totalCount)

  return (
    <div className="flex flex-col gap-4">
      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ComposedChart data={data} layout="vertical">
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            type="number"
            domain={[-30, 30]}
            tickFormatter={(value) => Math.abs(value).toString()}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            dataKey="age"
            type="number"
            domain={["dataMin", "dataMax"]}
            label={{ value: "Age", angle: -90, position: "insideLeft" }}
            tick={{ fontSize: 12 }}
          />
          <Tooltip />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Area
            dataKey="value"
            name="Full-time"
            stroke="#8884d8"
            fill="#8884d8"
            fillOpacity={0.6}
            data={violinPlotData.filter((d) => d.employeeType === "Full-time")}
          />
          <Area
            dataKey="value"
            name="Part-time"
            stroke="#82ca9d"
            fill="#82ca9d"
            fillOpacity={0.6}
            data={violinPlotData.filter((d) => d.employeeType === "Part-time")}
          />
        </ComposedChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2 text-center">
        <div className="rounded bg-blue-50 p-2">
          <p className="text-xs font-medium text-gray-500">Total Staff</p>
          <p className="text-lg font-bold text-blue-600">{totalStaff}</p>
        </div>
        <div className="rounded bg-green-50 p-2">
          <p className="text-xs font-medium text-gray-500">Employment Type</p>
          <p className="text-lg font-bold text-green-600">
            {fullTimePercent}% FT / {partTimePercent}% PT
          </p>
        </div>
        <div className="rounded bg-purple-50 p-2">
          <p className="text-xs font-medium text-gray-500">Average Age</p>
          <p className="text-lg font-bold text-purple-600">
            {averageAge} years
          </p>
        </div>
      </div>
    </div>
  )
}

export default StaffDemographicsChart
