import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON>ian<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample age distribution by occupation
const sampleAgeDistribution = [
  // Doctor age distribution
  { occupation: "Doctor", age: 25, count: 2 },
  { occupation: "Doctor", age: 28, count: 5 },
  { occupation: "Doctor", age: 31, count: 8 },
  { occupation: "Doctor", age: 34, count: 12 },
  { occupation: "Doctor", age: 37, count: 15 },
  { occupation: "Doctor", age: 40, count: 18 },
  { occupation: "Doctor", age: 43, count: 14 },
  { occupation: "Doctor", age: 46, count: 10 },
  { occupation: "Doctor", age: 49, count: 8 },
  { occupation: "Doctor", age: 52, count: 6 },
  { occupation: "Doctor", age: 55, count: 4 },
  { occupation: "Doctor", age: 58, count: 2 },

  // Clinic Assistant age distribution
  { occupation: "Clinic Assistant", age: 22, count: 4 },
  { occupation: "Clinic Assistant", age: 25, count: 8 },
  { occupation: "Clinic Assistant", age: 28, count: 12 },
  { occupation: "Clinic Assistant", age: 31, count: 15 },
  { occupation: "Clinic Assistant", age: 34, count: 18 },
  { occupation: "Clinic Assistant", age: 37, count: 20 },
  { occupation: "Clinic Assistant", age: 40, count: 16 },
  { occupation: "Clinic Assistant", age: 43, count: 12 },
  { occupation: "Clinic Assistant", age: 46, count: 8 },
  { occupation: "Clinic Assistant", age: 49, count: 6 },
  { occupation: "Clinic Assistant", age: 52, count: 4 },
  { occupation: "Clinic Assistant", age: 55, count: 2 },

  // Staff Nurse age distribution
  { occupation: "Staff Nurse", age: 23, count: 3 },
  { occupation: "Staff Nurse", age: 26, count: 6 },
  { occupation: "Staff Nurse", age: 29, count: 10 },
  { occupation: "Staff Nurse", age: 32, count: 14 },
  { occupation: "Staff Nurse", age: 35, count: 16 },
  { occupation: "Staff Nurse", age: 38, count: 18 },
  { occupation: "Staff Nurse", age: 41, count: 15 },
  { occupation: "Staff Nurse", age: 44, count: 12 },
  { occupation: "Staff Nurse", age: 47, count: 9 },
  { occupation: "Staff Nurse", age: 50, count: 6 },
  { occupation: "Staff Nurse", age: 53, count: 4 },
  { occupation: "Staff Nurse", age: 56, count: 2 },
]

// Sample staff count by occupation
const sampleStaffByOccupation = [
  { occupation: "Doctor", count: 104 },
  { occupation: "Clinic Assistant", count: 125 },
  { occupation: "Staff Nurse", count: 115 },
  { occupation: "Radiographer", count: 35 },
  { occupation: "Patient Relations Consultant", count: 28 },
  { occupation: "Dental Assistant", count: 22 },
  { occupation: "Executive", count: 18 },
  { occupation: "Manager", count: 15 },
  { occupation: "Sonographer", count: 12 },
  { occupation: "Healthcare Assistant", count: 10 },
]

// Create violin plot data by transforming the age distribution
const createViolinPlotData = () => {
  // Group by top 3 occupations
  const doctorData = sampleAgeDistribution.filter(
    (item) => item.occupation === "Doctor"
  )
  const clinicAssistantData = sampleAgeDistribution.filter(
    (item) => item.occupation === "Clinic Assistant"
  )
  const staffNurseData = sampleAgeDistribution.filter(
    (item) => item.occupation === "Staff Nurse"
  )

  // Find min and max ages
  const allAges = sampleAgeDistribution.map((item) => item.age)
  const minAge = Math.min(...allAges)
  const maxAge = Math.max(...allAges)

  // Create violin plot data points
  const violinData = []

  // For each age, create points for the three main occupations
  for (let age = minAge; age <= maxAge; age += 3) {
    const doctorPoint = doctorData.find((item) => item.age === age)
    const clinicAssistantPoint = clinicAssistantData.find(
      (item) => item.age === age
    )
    const staffNursePoint = staffNurseData.find((item) => item.age === age)

    const doctorCount = doctorPoint ? doctorPoint.count : 0
    const clinicAssistantCount = clinicAssistantPoint
      ? clinicAssistantPoint.count
      : 0
    const staffNurseCount = staffNursePoint ? staffNursePoint.count : 0

    // Create points for violin plot (using different positioning for 3 groups)
    violinData.push({
      age,
      occupation: "Doctor",
      count: doctorCount,
      // Negative values for left side
      value: -doctorCount,
    })

    violinData.push({
      age,
      occupation: "Clinic Assistant",
      count: clinicAssistantCount,
      // Center values
      value: clinicAssistantCount,
    })

    violinData.push({
      age,
      occupation: "Staff Nurse",
      count: staffNurseCount,
      // Positive values for right side (offset)
      value: staffNurseCount + 25,
    })
  }

  return violinData
}

const violinPlotData = createViolinPlotData()

interface StaffDemographicsChartProps {
  data?: typeof violinPlotData
}

const StaffDemographicsChart: React.FC<StaffDemographicsChartProps> = ({
  data = violinPlotData,
}) => {
  // Calculate overall metrics
  const totalStaff = sampleStaffByOccupation.reduce(
    (sum, item) => sum + item.count,
    0
  )
  const doctorCount =
    sampleStaffByOccupation.find((item) => item.occupation === "Doctor")
      ?.count || 0
  const clinicAssistantCount =
    sampleStaffByOccupation.find(
      (item) => item.occupation === "Clinic Assistant"
    )?.count || 0
  const staffNurseCount =
    sampleStaffByOccupation.find((item) => item.occupation === "Staff Nurse")
      ?.count || 0

  const topThreeCount = doctorCount + clinicAssistantCount + staffNurseCount
  const topThreePercent = Math.round((topThreeCount / totalStaff) * 100)

  // Calculate average age
  const totalAgeWeighted = sampleAgeDistribution.reduce(
    (sum, item) => sum + item.age * item.count,
    0
  )
  const totalCount = sampleAgeDistribution.reduce(
    (sum, item) => sum + item.count,
    0
  )
  const averageAge = Math.round(totalAgeWeighted / totalCount)

  return (
    <div className="flex flex-col gap-4">
      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ComposedChart data={data} layout="vertical">
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            type="number"
            domain={[-30, 30]}
            tickFormatter={(value) => Math.abs(value).toString()}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            dataKey="age"
            type="number"
            domain={["dataMin", "dataMax"]}
            label={{ value: "Age", angle: -90, position: "insideLeft" }}
            tick={{ fontSize: 12 }}
          />
          <Tooltip />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Area
            dataKey="value"
            name="Doctor"
            stroke="#8884d8"
            fill="#8884d8"
            fillOpacity={0.6}
            data={violinPlotData.filter((d) => d.occupation === "Doctor")}
          />
          <Area
            dataKey="value"
            name="Clinic Assistant"
            stroke="#82ca9d"
            fill="#82ca9d"
            fillOpacity={0.6}
            data={violinPlotData.filter(
              (d) => d.occupation === "Clinic Assistant"
            )}
          />
          <Area
            dataKey="value"
            name="Staff Nurse"
            stroke="#ff7c7c"
            fill="#ff7c7c"
            fillOpacity={0.6}
            data={violinPlotData.filter((d) => d.occupation === "Staff Nurse")}
          />
        </ComposedChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2 text-center">
        <div className="rounded bg-blue-50 p-2">
          <p className="text-xs font-medium text-gray-500">Total Staff</p>
          <p className="text-lg font-bold text-blue-600">{totalStaff}</p>
        </div>
        <div className="rounded bg-green-50 p-2">
          <p className="text-xs font-medium text-gray-500">Employment Type</p>
          <p className="text-lg font-bold text-green-600">
            {fullTimePercent}% FT / {partTimePercent}% PT
          </p>
        </div>
        <div className="rounded bg-purple-50 p-2">
          <p className="text-xs font-medium text-gray-500">Average Age</p>
          <p className="text-lg font-bold text-purple-600">
            {averageAge} years
          </p>
        </div>
      </div>
    </div>
  )
}

export default StaffDemographicsChart
