import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample performance rating distribution
const samplePerformanceDistribution = [
  { rating: "Exceptional", count: 28, percentage: 10.5 },
  { rating: "Exceeds Expectations", count: 72, percentage: 27.1 },
  { rating: "Meets Expectations", count: 124, percentage: 46.6 },
  { rating: "Needs Improvement", count: 35, percentage: 13.2 },
  { rating: "Unsatisfactory", count: 7, percentage: 2.6 },
]

interface StaffPerformanceChartProps {
  data?: typeof samplePerformanceDistribution
}

const StaffPerformanceChart: React.FC<StaffPerformanceChartProps> = ({
  data = samplePerformanceDistribution,
}) => {
  // Calculate overall metrics
  const totalStaff = samplePerformanceDistribution.reduce(
    (sum, item) => sum + item.count,
    0
  )
  const topPerformers = samplePerformanceDistribution
    .slice(0, 2)
    .reduce((sum, item) => sum + item.count, 0)
  const topPerformersPercent = Math.round((topPerformers / totalStaff) * 100)

  const needsImprovement = samplePerformanceDistribution
    .slice(3)
    .reduce((sum, item) => sum + item.count, 0)
  const needsImprovementPercent = Math.round(
    (needsImprovement / totalStaff) * 100
  )

  return (
    <div className="flex flex-col gap-4">
      <ResponsiveContainer width="99%" height={CHART_HEIGHT * 0.8}>
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="rating" tick={{ fontSize: 12 }} />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Bar dataKey="count" name="Staff Count" fill="#8884d8" />
        </BarChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2 text-center">
        <div className="rounded bg-blue-50 p-2">
          <p className="text-xs font-medium text-gray-500">Total Evaluated</p>
          <p className="text-lg font-bold text-blue-600">{totalStaff}</p>
        </div>
        <div className="rounded bg-green-50 p-2">
          <p className="text-xs font-medium text-gray-500">Top Performers</p>
          <p className="text-lg font-bold text-green-600">
            {topPerformersPercent}%
          </p>
        </div>
        <div className="rounded bg-red-50 p-2">
          <p className="text-xs font-medium text-gray-500">Needs Improvement</p>
          <p className="text-lg font-bold text-red-600">
            {needsImprovementPercent}%
          </p>
        </div>
      </div>
    </div>
  )
}

export default StaffPerformanceChart
