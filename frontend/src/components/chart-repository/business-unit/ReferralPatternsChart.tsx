import React from "react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface ReferralPatternsChartProps {
  data: {
    referralPatterns: Array<{
      source: string
      target: string
      value: number
    }>
    insuranceReferrals: Array<{
      source: string
      insuranceReferrals: number
    }>
    businessUnits: Array<{
      id: string
      name: string
      color: string
    }>
  }
}

const ReferralPatternsChart: React.FC<ReferralPatternsChartProps> = ({
  data,
}) => {
  // Aggregate referrals by source
  const referralsBySource = data.referralPatterns.reduce(
    (acc, { source, value }) => {
      if (!acc[source]) {
        acc[source] = { unit: source, totalReferrals: 0, insuranceReferrals: 0 }
      }
      acc[source].totalReferrals += value
      return acc
    },
    {} as Record<
      string,
      { unit: string; totalReferrals: number; insuranceReferrals: number }
    >
  )

  // Add insurance referrals data
  data.insuranceReferrals.forEach(({ source, insuranceReferrals }) => {
    if (referralsBySource[source]) {
      referralsBySource[source].insuranceReferrals = insuranceReferrals
    }
  })

  // Convert to array and sort by total referrals
  const chartData = Object.values(referralsBySource).sort(
    (a, b) => b.totalReferrals - a.totalReferrals
  )

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <ComposedChart data={chartData} layout="vertical">
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          type="number"
          tick={{ fontSize: 12 }}
          tickFormatter={(value) => formatAbbreviatedCurrency(value, 0)}
        />
        <YAxis
          type="category"
          dataKey="unit"
          width={120}
          tick={{ fontSize: 12 }}
        />
        <Tooltip
          formatter={(value, name, props) => {
            const formattedValue = `${formatAbbreviatedCurrency(Number(value), 0)} patients`

            if (name === "Insurance Referrals" && props.payload) {
              const totalReferrals = props.payload.totalReferrals
              const insuranceReferrals = Number(value)
              const percentage =
                totalReferrals > 0
                  ? ((insuranceReferrals / totalReferrals) * 100).toFixed(1)
                  : "0.0"
              return [`${formattedValue} (${percentage}%)`, name]
            }

            return [formattedValue, name]
          }}
        />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
        />
        <Bar dataKey="totalReferrals" name="Referrals Sent" fill="#8884d8" />
        <Line
          type="monotone"
          dataKey="insuranceReferrals"
          stroke="#ff7300"
          strokeWidth={2}
          dot={{ r: 4 }}
          name="Insurance Referrals"
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default ReferralPatternsChart
